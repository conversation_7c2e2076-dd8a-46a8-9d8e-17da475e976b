import {
  apCustomfield,
  ccAppointmentReq,
  ccCustomfieldReq,
  ccUserReq,
  contactReq,
  patientReq,
  serviceReq,
} from '@/request'
import { cLog } from '@/utils'
import Contact from 'App/Models/Contact'
import Appointment from '../Models/Appointment'
import { slackLogger } from '@/utils/slackLogger'
import Skip from '../Models/Skip'
import { removeNullEmptyProperties } from '@/utils/index'
import { resourceReq, ccLocationReq } from '@/request/cc'

export const getCCCustomfieldsLabelValue = async (
  contact: Contact
): Promise<{ [key: string]: any }> => {
  if (!contact.ccId) {
    throw new Error(
      `Patient ID not found, while get customfields, Contact ID: ${contact.id}, Email: ${
        contact.email ?? 'NA'
      } and Phone:- ${contact.phone ?? 'NA'}`
    )
  }
  if (contact.ccData.customFields.length < 1) {
    cLog(`Patient doesn't have customfields, Patient ID:- ${contact.ccId}`)
    return []
  }
  const patientCustomfields = await patientReq.customFields(contact.ccData.customFields)
  const labelValue: { [key: string]: any } = {}
  if (patientCustomfields.length > 0) {
    patientCustomfields.forEach((cf) => {
      const label = cf.field.label
      const values = cf.values.length > 0 ? cf.values.map((v) => v.value).join(', ') : null
      labelValue[label] = values
    })
  }
  return labelValue
}

export const getPatientById = async (ccId: number) => {
  return await patientReq.get(ccId)
}

export const getPatientContactById = async (patientId: number): Promise<Contact> => {
  const patient = await getPatientById(patientId)
  if (!patient) {
    throw new Error(`Patient ${patientId} does not exist.`)
  }
  if (!patient.email && !patient.phoneMobile) {
    cLog('Invalid data: Email and phone is empty.')
    slackLogger.error(`Invalid data: Email and phone is empty for patient ${patientId}`)
    throw new Error('Invalid data: Email and phone is empty.')
  }

  let contact = await Contact.searchCreateOrUpdate({
    email: patient.email,
    phone: patient.phoneMobile,
    ccId: patient.id,
    ccData: patient,
    source: 'cc',
  })
  return contact
}

export const searchPatient = async (contact: Contact) => {
  let patient
  if (!contact.phone && !contact.email) {
    throw new Error('Invalid data: Email and phone is missing')
  }
  if (contact.email) {
    patient = await patientReq.search(contact.email)
  }
  if (!patient && contact.phone) {
    patient = await patientReq.search(contact.phone)
  }
  if (!patient) {
    const payload: PostCCPatientType = {
      firstName: contact.apData.firstName,
      lastName: contact.apData.lastName,
      email: contact.email,
      phoneMobile: contact.phone,
    }
    if (contact.apId) {
      payload['customFields'] = await syncApToCcCustomfields(contact, true)
    }
    console.log('Creating patient to CC.')
    const patient = await patientReq.create(payload)
    contact.ccData = patient
    contact.ccId = patient.id
    await contact.save()
  } else {
    contact.ccData = patient
    contact.ccId = patient.id
    await contact.save()
  }
  return await contact.refresh()
}

export const updatePatientToCC = async (contact: Contact) => {
  if (!contact.ccId) {
    cLog(`Patient doesn't have CC ID, Creating it now.`)
    return await searchPatient(contact)
  }
  const payload: PostCCPatientType = {
    firstName: contact.apData.firstName,
    lastName: contact.apData.lastName,
    email: contact.email,
    phoneMobile: contact.phone,
  }
  if (contact.apId) {
    payload['customFields'] = await syncApToCcCustomfields(contact, true)
  }
  const patient = await patientReq.update(contact.ccId, payload)
  contact.ccData = patient
  await contact.save()
  return contact.refresh()
}

export const createPatientToCC = async (contact: Contact) => {
  if (contact.ccId) return contact.refresh()
  return await searchPatient(contact)
}

export const syncApToCcCustomfields = async (contact: Contact, returnValue = false) => {
  const apCustomFields = await apCustomfield.all()
  const apCFNameValue = {}
  if (contact.apData.customFields && contact.apData.customFields.length > 0) {
    contact.apData.customFields.map((cf) => {
      const match = apCustomFields.find((apcf) => apcf.id === cf.id)
      if (match) {
        apCFNameValue[match.name] = cf.value
      }
    })
  }

  apCFNameValue['email'] = contact.email
  apCFNameValue['phoneMobile'] = contact.phone
  apCFNameValue['phone-mobile'] = contact.phone
  apCFNameValue['phone'] = contact.phone
  const objectsToMatch = await ccCustomfieldReq.all()
  const matchedProperties: PostCCPatientCustomfield[] = []
  Object.keys(apCFNameValue).forEach((cf) => {
    const match = objectsToMatch.find((ccf) => ccf.name === cf || ccf.label === cf)
    if (match) {
      const value: PostCCPatientCustomfield = {
        field: match,
        values: [{ value: apCFNameValue[cf] }],
        patient: null,
      }
      if (match.allowedValues.length > 0) {
        const allowedValue = match.allowedValues.find((v) => v.value === apCFNameValue[cf])
        if (allowedValue) {
          value.values = [{ id: allowedValue.id }]
        }
      }
      matchedProperties.push(value)
    }
  })
  const payload = removeNullEmptyProperties(matchedProperties)
  if (returnValue) return payload
  if (!contact.ccId) {
    cLog(`Patient doesn't have CC ID, Preventing sync custom fields.`)
    return
  }
  const ccRes = await patientReq.update(contact.ccId, {
    customFields: payload,
  })
  if (ccRes) {
    contact.ccData = ccRes
    await contact.save()
    cLog(`Patient custom fields has been updated.`)
  } else {
    cLog(`Unable to update patient custom fields.`)
  }
}

export const createAppointmentToCC = async (appointment: Appointment) => {
  if (appointment.ccId) {
    cLog(
      `Seems like we already have this appointment in CC, Dropping this request. CC ID:- ${appointment.ccId}`
    )
    return
  }
  let contact = await Contact.findBy('apId', appointment.contactId)
  if (contact) {
    contact = await createPatientToCC(contact)
  }
  if (!contact?.ccId) {
    cLog(`Contact doesn't have CC ID, Dropping this request.`)
    slackLogger.error(
      `Unable to create or reterive patient from CC, AP Appointment ID: ${appointment.apId}`
    )
    throw new Error('Unable to create patient to CC')
  }
  const payload: any = {
    patients: [],
    startsAt: appointment.startAt.toISO() as string,
    endsAt: appointment.endAt.toISO() as string,
  }
  payload.patients.push(contact.ccId)
  await Skip.putProcessAppointmentCreate(appointment.apId)
  const ccRes = await ccAppointmentReq.post(payload)
  appointment.ccData = ccRes
  appointment.ccId = ccRes.id
  await appointment.save()
  const rApt = await appointment.refresh()
  syncApToCcAppointmentCustomfields(rApt)
  return rApt
}

export const updateAppointmentToCC = async (appointment: Appointment) => {
  if (!appointment.ccId) {
    cLog(`We don't have this appointment information, AP ID: ${appointment.apId}`)
    return
  }
  cLog(`Appointment status: ${appointment.apData.appointmentStatus}`)
  if (appointment.apData.appointmentStatus === 'cancelled') {
    console.log('Appointment canceling...')
    await cancelAppointmentToCC(appointment)
    return
  }
  const payload: any = {
    startsAt: appointment.startAt.toISO() as string,
    endsAt: appointment.endAt.toISO() as string,
    canceledWhy: null,
  }
  await Skip.putProcessAppointmentUpdate(appointment.ccId)
  const ccRes = await ccAppointmentReq.put(appointment.ccId, payload)
  if (ccRes && ccRes.id) {
    appointment.ccData = ccRes
    await appointment.save()
    cLog(
      `Appointment has been updated to CC, ID:- ${appointment.ccId}, Wil sync custom fields soon.`
    )
    syncApToCcAppointmentCustomfields(await appointment.refresh())
    return appointment
  }
  return null
}

const cancelAppointmentToCC = async (appointment: Appointment) => {
  if (appointment.apData.appointmentStatus === 'cancelled') {
    const ccPayload: any = {
      canceledWhy: 'cancelled at autoPatient',
    }
    const ccRes = await ccAppointmentReq.put(appointment.ccId, ccPayload)
    if (ccRes && ccRes.id) {
      appointment.ccData = ccRes
      await appointment.save()
      cLog(`Appointment has been cancelled to CC, ID:- ${appointment.ccId}`)
    }
  }
}

export const syncApToCcAppointmentCustomfields = async (appointment: Appointment) => {
  const apCustomFields = await apCustomfield.all()
  const apFields: string[] = [
    'AP Services',
    'AP People',
    'AP Resources',
    'AP Location',
    'AP Categories',
  ]
  let contact = await Contact.findBy('apId', appointment.contactId)
  if (!contact || !contact.ccId || !contact.apId) {
    cLog(`Contact doesn't have CC ID or AP ID, Preventing syncing appointment custom fields.`)
    slackLogger.error(
      `Contact doesn't have CC ID or AP ID, Preventing syncing appointment custom fields, AP Appointment ID: ${appointment.apId}`
    )
    return
  }
  const apCFNameValue = {}

  const apCRes = await contactReq.get(contact.apId)
  if (apCRes && apCRes.id) {
    contact.apData = apCRes
    await contact.save()
    contact = await contact.refresh()
  }

  if (contact.apData.customFields && contact.apData.customFields.length > 0) {
    contact.apData.customFields.map((cf) => {
      const match = apCustomFields.find((apcf) => apcf.id === cf.id)
      if (match && apFields.includes(match.name)) {
        apCFNameValue[match.name] = cf.value
      }
    })
  } else {
    cLog(`No custom fields found for this contact, Contact ID: ${appointment.contactId}`)
    return
  }

  if (Object.keys(apCFNameValue).length === 0) {
    cLog(
      `No appointment custom fields found for this contact, Contact ID: ${appointment.contactId}`
    )
    return
  }

  const ccAppointmentPayload: PutCCAppointmentType = {}

  if (apCFNameValue['AP Services']) {
    const pS: number[] = []
    const services = apCFNameValue['AP Services']
      .toString()
      .toLowerCase()
      .split(',')
      .map((r: string) => r.toString().trim())
    const ccServices = await serviceReq.all()
    if (ccServices && ccServices.length > 0) {
      ccServices.map((s) => {
        if (
          services.includes(s.name.toLowerCase()) ||
          services.includes(s.externalName.toLowerCase())
        ) {
          pS.push(s.id)
        }
      })
    }
    ccAppointmentPayload['services'] = pS
  }

  if (apCFNameValue['AP People']) {
    ccAppointmentPayload['people'] = []
    const people = apCFNameValue['AP People']
      .toString()
      .toLowerCase()
      .split(',')
      .map((r: string) => r.toString().trim())
    const ccPeople = await ccUserReq.all()
    if (ccPeople && ccPeople.length > 0) {
      ccPeople.map((p) => {
        if (
          people.includes(p.firstName.toLowerCase()) ||
          people.includes(p.lastName.toLowerCase()) ||
          people.includes(p.firstName.toLowerCase() + ' ' + p.lastName.toLowerCase()) ||
          (p.externalName && people.includes(p.externalName.toLowerCase()))
        ) {
          ccAppointmentPayload['people']?.push(p.id)
        }
      })
    }
  }

  if (apCFNameValue['AP Resources']) {
    const rs: number[] = []
    const resources = apCFNameValue['AP Resources']
      .toString()
      .toLowerCase()
      .split(',')
      .map((r: string) => r.toString().trim())
    const ccResources = await resourceReq.all()
    if (ccResources && ccResources.length > 0) {
      ccResources.map((r) => {
        if (
          resources.includes(r.name.toLowerCase()) ||
          (r.shortName && resources.includes(r.shortName.toLowerCase()))
        ) {
          rs.push(r.id)
        }
      })
    }
    ccAppointmentPayload['resources'] = rs
  }

  if (apCFNameValue['AP Location']) {
    const location = apCFNameValue['AP Location'].toString().toLowerCase()
    const ccLocations = await ccLocationReq.all()
    if (ccLocations && ccLocations.length > 0) {
      ccLocations.map((l) => {
        if (l.name && l.name.toLowerCase() === location) {
          ccAppointmentPayload['location'] = l.id
        } else if (l.shortName && l.shortName.toLowerCase() === location) {
          ccAppointmentPayload['location'] = l.id
        }
      })
    }
  }

  if (apCFNameValue['AP Categories']) {
    const cat: number[] = []
    const categories = apCFNameValue['AP Categories']
      .toString()
      .toLowerCase()
      .split(',')
      .map((r: string) => r.toString().trim())
    const ccCategories = await ccAppointmentReq.category.all()
    if (ccCategories && ccCategories.length > 0) {
      ccCategories.map((c) => {
        if (
          categories.includes(c.title.toLowerCase()) ||
          categories.includes(c.shortTitle.toLowerCase())
        ) {
          cat.push(c.id)
        }
      })
    }
    ccAppointmentPayload['categories'] = cat
  }

  const fPayload = removeNullEmptyProperties(ccAppointmentPayload)

  if (Object.keys(fPayload).length > 0) {
    await Skip.putProcessAppointmentUpdate(appointment.ccId)
    const ccRes = await ccAppointmentReq.put(appointment.ccId, ccAppointmentPayload)
    if (ccRes && ccRes.id) {
      appointment.ccData = ccRes
      await appointment.save()
      cLog(`Appointment customfields has been updated to CC, ID:- ${appointment.ccId}`)
    } else {
      cLog(`Appointment customfields has not been updated to CC, ID:- ${appointment.ccId}`)
    }
  } else {
    cLog(`No appointment custom fields found to update to CC, ID:- ${appointment.ccId}`)
  }
}

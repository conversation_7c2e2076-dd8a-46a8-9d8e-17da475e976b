/**
 * Debug script to test allowedValues ID mapping
 * 
 * This script tests the complete data flow from value conversion
 * through to the final API payload to identify where the ID mapping
 * is being lost.
 */

import { validateAndMapAllowedValues } from "@/processors/patientCustomFields/valueConverters";
import { convertApValueToCc } from "@/processors/patientCustomFields/valueConverters";
import cleanData from "@/utils/cleanData";
import type { GetCCCustomField, CCFieldPayload } from "@/type/CCTypes";
import type { APCustomFieldValue, FieldMapping } from "@/processors/patientCustomFields/types";

/**
 * Test the complete allowedValues mapping flow
 */
export function testAllowedValuesMapping() {
	console.log("=".repeat(80));
	console.log("TESTING ALLOWEDVALUES ID MAPPING FLOW");
	console.log("=".repeat(80));

	// Mock CC field with allowedValues
	const mockCcField: GetCCCustomField = {
		id: 123,
		name: "test-field",
		label: "Test Field",
		type: "select",
		validation: "",
		color: null,
		allowMultipleValues: false,
		useCustomSort: null,
		isRequired: false,
		positions: [],
		allowedValues: [
			{ id: 1, value: "Option A", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 2, value: "Option B", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
			{ id: 3, value: "Option C", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
		],
		defaultValues: [],
	};

	// Mock AP field value
	const mockApValue: APCustomFieldValue = {
		id: "ap-field-123",
		value: "Option B", // This should map to ID 2
	};

	// Mock field mapping
	const mockMapping: FieldMapping = {
		id: "mapping-123",
		apFieldId: "ap-field-123",
		ccFieldId: 123,
		apConfig: {
			id: "ap-field-123",
			name: "test-field",
			dataType: "TEXT",
		} as any,
		ccConfig: mockCcField,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	console.log("\n1. TESTING validateAndMapAllowedValues()");
	console.log("-".repeat(50));
	
	const testValues = ["Option B", "Option A", "Invalid Option"];
	const mappedValues = validateAndMapAllowedValues(testValues, mockCcField);
	
	console.log("Input values:", testValues);
	console.log("Mapped values:", JSON.stringify(mappedValues, null, 2));
	console.log("Expected: [{ id: 2 }, { id: 1 }, { value: 'Invalid Option' }]");

	console.log("\n2. TESTING convertApValueToCc()");
	console.log("-".repeat(50));
	
	const conversionResult = convertApValueToCc(mockApValue, mockMapping);
	
	console.log("Input AP value:", JSON.stringify(mockApValue, null, 2));
	console.log("Conversion result:", JSON.stringify(conversionResult, null, 2));
	
	if (conversionResult.success && conversionResult.convertedValue) {
		console.log("Converted CC values:", JSON.stringify(conversionResult.convertedValue.values, null, 2));
	}

	console.log("\n3. TESTING CCFieldPayload construction");
	console.log("-".repeat(50));
	
	if (conversionResult.success && conversionResult.convertedValue) {
		const ccValue = conversionResult.convertedValue;
		const payload: CCFieldPayload = {
			field: { id: ccValue.field.id.toString() },
			values: ccValue.values,
		};
		
		console.log("CCFieldPayload:", JSON.stringify(payload, null, 2));
	}

	console.log("\n4. TESTING cleanData() transformation");
	console.log("-".repeat(50));
	
	const testPayload = {
		customFields: [
			{
				field: { id: "123" },
				values: [
					{ id: 2 },
					{ value: "Custom Value" }
				]
			}
		]
	};
	
	console.log("Before cleanData:", JSON.stringify(testPayload, null, 2));
	
	// Test default cleanData
	const cleanedDefault = cleanData(testPayload);
	console.log("After cleanData (default):", JSON.stringify(cleanedDefault, null, 2));
	
	// Test cleanData with custom options (like in our fix)
	const cleanedCustom = cleanData(testPayload, {
		keepValue: (value, key) => {
			if (key === "customFields") return true;
			if (typeof value === "object" && value !== null && "id" in value) return true;
			if (typeof value === "object" && value !== null && "value" in value) return true;
			return false;
		},
	});
	console.log("After cleanData (custom):", JSON.stringify(cleanedCustom, null, 2));

	console.log("\n5. TESTING final HTTP payload structure");
	console.log("-".repeat(50));
	
	const finalPayload = {
		patient: cleanedCustom
	};
	
	console.log("Final HTTP payload:", JSON.stringify(finalPayload, null, 2));
	console.log("JSON.stringify() result:", JSON.stringify(finalPayload));

	console.log("\n" + "=".repeat(80));
	console.log("TEST COMPLETE");
	console.log("=".repeat(80));
}

/**
 * Test specific edge cases
 */
export function testEdgeCases() {
	console.log("\n" + "=".repeat(80));
	console.log("TESTING EDGE CASES");
	console.log("=".repeat(80));

	// Test empty object handling
	const emptyIdObject = { id: 0 };
	const emptyValueObject = { value: "" };
	
	console.log("\nTesting empty objects:");
	console.log("Empty ID object:", JSON.stringify(emptyIdObject));
	console.log("Empty value object:", JSON.stringify(emptyValueObject));
	
	const cleanedEmptyId = cleanData(emptyIdObject);
	const cleanedEmptyValue = cleanData(emptyValueObject);
	
	console.log("After cleanData - empty ID:", JSON.stringify(cleanedEmptyId));
	console.log("After cleanData - empty value:", JSON.stringify(cleanedEmptyValue));
	
	// Test with custom keepValue
	const cleanedEmptyIdCustom = cleanData(emptyIdObject, {
		keepValue: (value, key) => key === "id" || (typeof value === "object" && value !== null && "id" in value)
	});
	
	console.log("After cleanData (custom) - empty ID:", JSON.stringify(cleanedEmptyIdCustom));
}

// Export for use in other files
export { testAllowedValuesMapping as default };

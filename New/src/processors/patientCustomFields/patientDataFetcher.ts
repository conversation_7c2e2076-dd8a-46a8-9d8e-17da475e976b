/**
 * Patient Data Fetcher
 *
 * Handles fetching patient data with custom field values from both
 * AutoPatient and CliniCore platforms. Provides unified interface
 * for patient data retrieval with proper error handling.
 *
 * @fileoverview Patient data fetching utilities
 * @version 1.0.0
 * @since 2024-07-29
 */

import apiClient from "@apiClient";
import { dbSchema, getDb } from "@database";
import { eq } from "drizzle-orm";
import type { CCFieldPayload, PostCCPatientType } from "@/type";
import { getRequestId } from "@/utils";
import { logError, logInfo, logWarn } from "@/utils/logger";
import {
	hasTextboxListFields,
	processTextboxListFields,
} from "./textboxListProcessor";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	PatientData,
	PatientLookupResult,
} from "./types";

/**
 * Fetch patient data from AutoPatient platform
 *
 * @param patientId - Local database patient ID
 * @returns Patient data with custom field values
 */
export async function fetchApPatientData(
	patientId: string,
): Promise<PatientData | null> {
	const requestId = getRequestId();
	try {
		// First, get the patient lookup to find AP contact ID
		const lookup = await lookupPatientIds(patientId, requestId);
		if (!lookup.found || !lookup.apId) {
			logError("AP contact ID not found for patient", {
				requestId,
				patientId,
				lookup,
			});
			return null;
		}

		// Fetch contact data with custom fields
		const contact = await apiClient.ap.contactReq.get(lookup.apId);

		logInfo("Fetched AP contact data", {
			requestId,
			patientId,
			apContactId: lookup.apId,
			customFieldCount: contact.customFields?.length || 0,
		});

		// Convert to standardized format
		const customFields: APCustomFieldValue[] =
			contact.customFields?.map((cf) => ({
				id: cf.id,
				value: cf.value,
			})) || [];

		return {
			id: lookup.apId,
			customFields,
		};
	} catch (error) {
		logError("Failed to fetch AP patient data", {
			requestId,
			patientId,
			error: String(error),
		});
		return null;
	}
}

/**
 * Fetch patient data from CliniCore platform
 *
 * @param patientId - Local database patient ID
 * @returns Patient data with custom field values
 */
export async function fetchCcPatientData(
	patientId: string,
): Promise<PatientData | null> {
	const requestId = getRequestId();
	try {
		// First, get the patient lookup to find CC patient ID
		const lookup = await lookupPatientIds(patientId, requestId);
		if (!lookup.found || !lookup.ccId) {
			logError("CC patient ID not found for patient", {
				requestId,
				patientId,
				lookup,
			});
			return null;
		}

		// Fetch patient data with cache invalidation to ensure fresh data
		const patient = await apiClient.cc.patientReq.get(lookup.ccId, true);
		// Fetch custom field values if patient has custom fields
		let customFields: CCCustomFieldValue[] = [];
		if (patient.customFields && patient.customFields.length > 0) {
			const customFieldData = await apiClient.cc.patientReq.customFields(
				patient.customFields,
				true,
			);
			customFields = customFieldData.map((cf) => ({
				field: cf.field,
				values: cf.values || [],
				patient: cf.patient || null,
			}));
		}

		return {
			id: lookup.ccId,
			customFields,
		};
	} catch (error) {
		logError("Failed to fetch CC patient data", {
			requestId,
			patientId,
			error: String(error),
		});
		return null;
	}
}

/**
 * Look up patient IDs from local database
 *
 * Queries the local patient table to find the corresponding AP contact ID
 * and CC patient ID for a given local patient ID.
 *
 * @param patientId - Local database patient ID
 * @param requestId - Request ID for logging
 * @returns Patient lookup result with AP and CC IDs
 */
export async function lookupPatientIds(
	patientId: string,
	requestId: string,
): Promise<PatientLookupResult> {
	try {
		const db = getDb();
		const patients = await db
			.select({
				apId: dbSchema.patient.apId,
				ccId: dbSchema.patient.ccId,
			})
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.id, patientId));

		if (patients.length === 0) {
			logWarn("Patient not found in database", {
				requestId,
				patientId,
			});

			return {
				found: false,
				error: "Patient not found in database",
			};
		}

		const patient = patients[0];
		const result: PatientLookupResult = {
			found: true,
			apId: patient.apId || undefined,
			ccId: patient.ccId || undefined,
		};

		logInfo("Patient lookup completed", {
			requestId,
			patientId,
			result: {
				found: result.found,
				hasApId: !!result.apId,
				hasCcId: !!result.ccId,
			},
		});

		return result;
	} catch (error) {
		logError("Failed to lookup patient IDs", {
			requestId,
			patientId,
			error: String(error),
		});

		return {
			found: false,
			error: String(error),
		};
	}
}

/**
 * Update AutoPatient contact with custom field values
 *
 * @param contactId - AP contact ID
 * @param customFields - Custom field values to update
 * @returns Success status
 */
export async function updateApPatientCustomFields(
	contactId: string,
	customFields: APCustomFieldValue[],
): Promise<boolean> {
	try {
		// Check if we have TEXTBOX_LIST fields that need processing
		if (hasTextboxListFields(customFields)) {
			logInfo("Processing TEXTBOX_LIST fields before update", {
				contactId,
				totalFields: customFields.length,
				textboxListFields: customFields.filter(
					(f) =>
						typeof f.value === "string" &&
						f.value.startsWith("__TEXTBOX_LIST_VALUES__:"),
				).length,
			});

			// Fetch AP field configurations for TEXTBOX_LIST processing
			const apFields = await apiClient.ap.apCustomfield.allWithParentFilter();
			const apFieldsMap = new Map(apFields.map((field) => [field.id, field]));

			// Fetch current contact data to get existing TEXTBOX_LIST values for clearing
			const currentContact = await apiClient.ap.contactReq.get(contactId);

			// Process TEXTBOX_LIST fields
			const processingResult = await processTextboxListFields(
				customFields,
				apFieldsMap,
				currentContact,
			);

			if (!processingResult.success) {
				logError("Failed to process TEXTBOX_LIST fields", {
					contactId,
					errors: processingResult.errors,
					warnings: processingResult.warnings,
				});
				// Continue with original fields if processing fails
			} else {
				// Use processed fields
				customFields = processingResult.processedFields;
			}
		}

		// Prepare update data with proper field_value support
		const updateData = {
			customFields: customFields.map((cf) => {
				if (cf.field_value) {
					// TEXTBOX_LIST field with field_value format
					return {
						id: cf.id,
						field_value: cf.field_value,
					};
				} else {
					// Regular field with value format
					return {
						id: cf.id,
						value: cf.value as string | number,
					};
				}
			}),
		};

		await apiClient.ap.contactReq.update(contactId, updateData);

		return true;
	} catch (error) {
		const textboxListCount = customFields.filter(
			(cf) => cf.value && typeof cf.value === "object",
		).length;
		logError("Failed to update AP patient custom fields", {
			contactId,
			fieldCount: customFields.length,
			textboxListCount,
			error: String(error),
		});
		return false;
	}
}

/**
 * Update CliniCore patient with custom field values
 *
 * CC API updates custom field values through the patient update endpoint.
 * The payload includes the custom field definitions and values.
 *
 * @param patientId - CC patient ID
 * @param customFields - Custom field values to update
 * @param requestId - Request ID for logging
 * @returns Success status
 */
export async function updateCcPatientCustomFields(
	patientId: number,
	customFields: CCCustomFieldValue[],
): Promise<boolean> {
	try {
		// Validate and prepare the custom fields payload for CC API
		const customFieldsPayload: CCFieldPayload[] = [];

		for (const cf of customFields) {
			// Validate values that reference allowedValues
			const validatedValues: Array<{ id: number } | { value: string }> = [];

			for (const value of cf.values) {
				if (value.id !== undefined) {
					// Ensure ID is a number
					const numericId = typeof value.id === 'string' ? parseInt(value.id, 10) : value.id;

					// Validate that the ID exists in allowedValues
					if (cf.field.allowedValues && cf.field.allowedValues.length > 0) {
						const allowedValue = cf.field.allowedValues.find(av => av.id === numericId);

						if (allowedValue) {
							validatedValues.push({ id: numericId });
						} else {
							logWarn("Invalid value ID for CC custom field - ID not found in allowedValues", {
								patientId,
								fieldName: cf.field.name,
								fieldId: cf.field.id,
								invalidValueId: numericId,
								availableValueIds: cf.field.allowedValues.map(av => av.id),
								issue: "Value ID does not exist in field's allowedValues array"
							});
							// Skip this invalid value
							continue;
						}
					} else {
						// Field has no allowedValues but value has ID - this shouldn't happen
						logWarn("Value ID provided for CC custom field without allowedValues", {
							patientId,
							fieldName: cf.field.name,
							fieldId: cf.field.id,
							valueId: numericId,
							issue: "Field has no allowedValues but value contains ID"
						});
						// Skip this invalid value
						continue;
					}
				} else if (value.value !== undefined) {
					// Regular value (not referencing allowedValues)
					validatedValues.push({ value: value.value });
				}
			}

			// Only include fields that have valid values
			if (validatedValues.length > 0) {
				customFieldsPayload.push({
					field: { id: cf.field.id.toString() },
					values: validatedValues,
				});
			} else {
				logWarn("CC custom field has no valid values after validation", {
					patientId,
					fieldName: cf.field.name,
					fieldId: cf.field.id,
					originalValueCount: cf.values.length,
					issue: "All values were invalid and filtered out"
				});
			}
		}

		// Update patient with custom fields
		const updateData: Omit<PostCCPatientType, "customFields"> & {
			customFields?: CCFieldPayload[];
		} = {
			customFields: customFieldsPayload,
		};

		// Enhanced logging to debug the exact payload structure
		logInfo("CC Patient Update - Final payload structure before API call", {
			patientId,
			updateDataKeys: Object.keys(updateData),
			customFieldsCount: customFieldsPayload.length,
			customFieldsPayload: customFieldsPayload.map(cf => ({
				fieldId: cf.field.id,
				valuesCount: cf.values.length,
				values: cf.values.map(v => ({
					hasId: 'id' in v,
					hasValue: 'value' in v,
					idValue: 'id' in v ? v.id : undefined,
					valueValue: 'value' in v ? v.value : undefined,
					objectKeys: Object.keys(v),
				})),
			})),
		});

		await apiClient.cc.patientReq.update(
			patientId,
			updateData as PostCCPatientType,
		);

		logInfo("Successfully updated CC patient custom fields", {
			patientId,
			fieldCount: customFields.length,
			validatedFieldCount: customFieldsPayload.length,
		});

		return true;
	} catch (error) {
		logError("Failed to update CC patient custom fields", {
			patientId,
			fieldCount: customFields.length,
			error: String(error),
		});
		return false;
	}
}

/**
 * Debug endpoint for testing allowedValues ID mapping
 */

import { Hono } from "hono";
import { testAllowedValuesMapping, testEdgeCases } from "@/debug/allowedValuesTest";

const app = new Hono();

/**
 * Test allowedValues ID mapping flow
 * GET /debug/allowed-values-test
 */
app.get("/", async (c) => {
	try {
		// Capture console output
		const originalLog = console.log;
		const logs: string[] = [];
		
		console.log = (...args) => {
			logs.push(args.join(" "));
			originalLog(...args);
		};

		// Run tests
		testAllowedValuesMapping();
		testEdgeCases();

		// Restore console.log
		console.log = originalLog;

		return c.json({
			success: true,
			message: "AllowedValues ID mapping test completed",
			logs: logs,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("Error running allowedValues test:", error);
		return c.json({
			success: false,
			error: String(error),
			timestamp: new Date().toISOString(),
		}, 500);
	}
});

export default app;
